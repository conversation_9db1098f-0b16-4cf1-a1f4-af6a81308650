<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import * as THREE from 'three';
  //导入轨道控制器
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
  import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
  import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';

  //创建场景
  const scene = new THREE.Scene();

  //创建相机
  const camera = new THREE.PerspectiveCamera(
    45, //视角
    window.innerWidth / window.innerHeight, //宽高比
    0.1, //近裁剪面
    1000 //远裁剪面
  );
  //设置相机位置
  camera.position.z = 5;
  camera.position.y = 2;
  camera.position.x = 2;
  //设置相机看向的位置
  camera.lookAt(0, 0, 0);

  //添加世界坐标辅助器
  const axesHelper = new THREE.AxesHelper(5);
  scene.add(axesHelper);

  //创建立方体
  const boxGeometry = new THREE.BoxGeometry(1, 1, 100);
  const boxMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
  const box = new THREE.Mesh(boxGeometry, boxMaterial);
  scene.add(box);

  // scene.fog = new THREE.Fog(0x999999, 0.1, 50);
  scene.fog = new THREE.FogExp2(0x999999, 0.5);
  scene.background = new THREE.Color(0x999999);

  //创建渲染器（延后到 onMounted，通过模板里的 canvas 进行渲染）
  let renderer: THREE.WebGLRenderer;
  //将渲染器添加到页面中
  const canvas = ref<HTMLCanvasElement | null>(null);

  // 让动画循环可访问控制器
  let controls: OrbitControls | null = null;

  //创建GUI
  const enventObj = {
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.body.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    },
  };
  const gui = new GUI();
  //添加按钮
  gui.add(enventObj, 'toggleFullscreen').name('全屏切换');

  //渲染函数
  function animate() {
    requestAnimationFrame(animate);
    // 每帧更新控制器（阻尼/自动旋转）
    controls?.update();
    //渲染
    renderer.render(scene, camera);
  }

  onMounted(() => {
    if (canvas.value) {
      renderer = new THREE.WebGLRenderer({ canvas: canvas.value });
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      renderer.setSize(window.innerWidth, window.innerHeight);

      //添加轨道控制器
      controls = new OrbitControls(camera, renderer.domElement);

      animate();
    }
  });
</script>

<style scoped>
  canvas {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
